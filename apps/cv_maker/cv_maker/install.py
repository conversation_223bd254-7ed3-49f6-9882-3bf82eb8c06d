# Copyright (c) 2025, Your Organization and contributors
# For license information, please see license.txt

import frappe
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields


def after_install():
	"""Run after app installation"""
	create_custom_fields_for_employee()
	create_default_templates()
	frappe.db.commit()


def create_custom_fields_for_employee():
	"""Add custom fields to Employee doctype"""
	custom_fields = {
		"Employee": [
			{
				"fieldname": "cv_section",
				"fieldtype": "Section Break",
				"label": "CV Information",
				"insert_after": "personal_details"
			},
			{
				"fieldname": "cv_profile_link",
				"fieldtype": "Link",
				"label": "CV Profile",
				"options": "Employee CV Profile",
				"read_only": 1,
				"insert_after": "cv_section"
			}
		]
	}
	
	create_custom_fields(custom_fields, update=True)


def create_default_templates():
	"""Create default CV templates if they don't exist"""
	import os

	templates = [
		{
			"name": "Minimalist CV",
			"template_type": "Minimalist",
			"description": "Clean and simple CV template with minimal design elements",
			"is_default": 1,
			"file": "minimalist.html"
		},
		{
			"name": "Corporate CV",
			"template_type": "Corporate",
			"description": "Professional corporate-style CV template with formal design",
			"is_default": 0,
			"file": "corporate.html"
		},
		{
			"name": "Creative CV",
			"template_type": "Creative",
			"description": "Modern and creative CV template with colorful design elements",
			"is_default": 0,
			"file": "creative.html"
		}
	]

	for template_data in templates:
		try:
			if not frappe.db.exists("CV Template", template_data["name"]):
				# Read template file
				template_path = frappe.get_app_path("cv_maker", "templates", "cv_templates", template_data["file"])

				if os.path.exists(template_path):
					with open(template_path, 'r', encoding='utf-8') as f:
						template_html = f.read()
				else:
					# Use a simple default template if file doesn't exist
					template_html = f"""
					<html>
					<head><title>{{{{ employee_name }}}} - CV</title></head>
					<body>
						<h1>{{{{ employee_name }}}}</h1>
						<h2>{{{{ cv_title }}}}</h2>
						<p>{{{{ summary }}}}</p>
					</body>
					</html>
					"""

				# Create template document
				template = frappe.new_doc("CV Template")
				template.template_name = template_data["name"]
				template.template_type = template_data["template_type"]
				template.description = template_data["description"]
				template.is_default = template_data["is_default"]
				template.status = "Active"
				template.template_html = template_html
				template.save(ignore_permissions=True)

				frappe.logger().info(f"Created CV Template: {template_data['name']}")

		except Exception as e:
			frappe.logger().error(f"Error creating template {template_data['name']}: {str(e)}")
			# Continue with other templates even if one fails
			continue


def create_sample_data():
	"""Create sample CV data for demonstration"""
	# This function can be called manually to create sample data
	if frappe.db.exists("Employee", "EMP-001"):
		return
	
	# Create sample employee
	employee = frappe.new_doc("Employee")
	employee.employee_name = "John Doe"
	employee.first_name = "John"
	employee.last_name = "Doe"
	employee.gender = "Male"
	employee.date_of_birth = "1990-01-15"
	employee.date_of_joining = "2020-01-01"
	employee.designation = "Software Developer"
	employee.department = "Technology"
	employee.company = "Sample Company"
	employee.personal_email = "<EMAIL>"
	employee.cell_number = "+1234567890"
	employee.save()
	
	# Create CV Profile
	cv_profile = frappe.new_doc("Employee CV Profile")
	cv_profile.employee = employee.name
	cv_profile.cv_title = "Senior Software Developer"
	cv_profile.summary = "Experienced software developer with 5+ years of expertise in web development, database design, and system architecture. Passionate about creating efficient, scalable solutions and mentoring junior developers."
	cv_profile.cv_visibility = "Internal"
	
	# Add skills
	skills_data = [
		{"skill_name": "Python", "proficiency_level": "Expert", "years_of_experience": 5},
		{"skill_name": "JavaScript", "proficiency_level": "Advanced", "years_of_experience": 4},
		{"skill_name": "React", "proficiency_level": "Advanced", "years_of_experience": 3},
		{"skill_name": "SQL", "proficiency_level": "Expert", "years_of_experience": 5},
		{"skill_name": "Docker", "proficiency_level": "Intermediate", "years_of_experience": 2}
	]
	
	for skill_data in skills_data:
		cv_profile.append("skills", skill_data)
	
	# Add projects
	projects_data = [
		{
			"project_title": "E-commerce Platform",
			"client": "ABC Corp",
			"role": "Lead Developer",
			"duration_from": "2023-01-01",
			"duration_to": "2023-12-31",
			"description": "Developed a comprehensive e-commerce platform handling 10,000+ daily transactions",
			"technologies": "Python, Django, PostgreSQL, Redis, AWS"
		},
		{
			"project_title": "Mobile Banking App",
			"client": "XYZ Bank",
			"role": "Backend Developer",
			"duration_from": "2022-06-01",
			"duration_to": "2022-12-31",
			"description": "Built secure backend APIs for mobile banking application",
			"technologies": "Node.js, Express, MongoDB, JWT"
		}
	]
	
	for project_data in projects_data:
		cv_profile.append("projects", project_data)
	
	# Add certifications
	certifications_data = [
		{
			"certification_name": "AWS Certified Solutions Architect",
			"issuing_organization": "Amazon Web Services",
			"date_awarded": "2023-06-15",
			"expiry_date": "2026-06-15"
		},
		{
			"certification_name": "Certified Kubernetes Administrator",
			"issuing_organization": "Cloud Native Computing Foundation",
			"date_awarded": "2023-03-20",
			"expiry_date": "2026-03-20"
		}
	]
	
	for cert_data in certifications_data:
		cv_profile.append("certifications", cert_data)
	
	# Add languages
	languages_data = [
		{"language": "English", "proficiency": "Native"},
		{"language": "Spanish", "proficiency": "Professional"},
		{"language": "French", "proficiency": "Basic"}
	]
	
	for lang_data in languages_data:
		cv_profile.append("languages", lang_data)
	
	cv_profile.save()
	
	frappe.logger().info("Sample CV data created successfully")
