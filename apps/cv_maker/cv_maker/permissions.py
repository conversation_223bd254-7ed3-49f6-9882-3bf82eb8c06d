# Copyright (c) 2025, Your Organization and contributors
# For license information, please see license.txt

import frappe


def setup_permissions():
	"""Setup role-based permissions for CV Manager"""
	
	# Define role permissions
	permissions_config = {
		"Employee CV Profile": [
			{
				"role": "System Manager",
				"permlevel": 0,
				"read": 1,
				"write": 1,
				"create": 1,
				"delete": 1,
				"submit": 0,
				"cancel": 0,
				"amend": 0,
				"report": 1,
				"export": 1,
				"print": 1,
				"email": 1,
				"share": 1
			},
			{
				"role": "HR Manager",
				"permlevel": 0,
				"read": 1,
				"write": 1,
				"create": 1,
				"delete": 1,
				"submit": 0,
				"cancel": 0,
				"amend": 0,
				"report": 1,
				"export": 1,
				"print": 1,
				"email": 1,
				"share": 1
			},
			{
				"role": "Employee",
				"permlevel": 0,
				"read": 1,
				"write": 1,
				"create": 1,
				"delete": 0,
				"submit": 0,
				"cancel": 0,
				"amend": 0,
				"report": 0,
				"export": 1,
				"print": 1,
				"email": 1,
				"share": 1,
				"if_owner": 1
			}
		],
		"CV Template": [
			{
				"role": "System Manager",
				"permlevel": 0,
				"read": 1,
				"write": 1,
				"create": 1,
				"delete": 1,
				"submit": 0,
				"cancel": 0,
				"amend": 0,
				"report": 1,
				"export": 1,
				"print": 1,
				"email": 1,
				"share": 1
			},
			{
				"role": "HR Manager",
				"permlevel": 0,
				"read": 1,
				"write": 1,
				"create": 1,
				"delete": 1,
				"submit": 0,
				"cancel": 0,
				"amend": 0,
				"report": 1,
				"export": 1,
				"print": 1,
				"email": 1,
				"share": 1
			},
			{
				"role": "Employee",
				"permlevel": 0,
				"read": 1,
				"write": 0,
				"create": 0,
				"delete": 0,
				"submit": 0,
				"cancel": 0,
				"amend": 0,
				"report": 0,
				"export": 0,
				"print": 1,
				"email": 0,
				"share": 0
			}
		],
		"CV Pack": [
			{
				"role": "System Manager",
				"permlevel": 0,
				"read": 1,
				"write": 1,
				"create": 1,
				"delete": 1,
				"submit": 0,
				"cancel": 0,
				"amend": 0,
				"report": 1,
				"export": 1,
				"print": 1,
				"email": 1,
				"share": 1
			},
			{
				"role": "HR Manager",
				"permlevel": 0,
				"read": 1,
				"write": 1,
				"create": 1,
				"delete": 1,
				"submit": 0,
				"cancel": 0,
				"amend": 0,
				"report": 1,
				"export": 1,
				"print": 1,
				"email": 1,
				"share": 1
			}
		]
	}
	
	# Apply permissions
	for doctype, perms in permissions_config.items():
		# Clear existing permissions
		frappe.db.delete("DocPerm", {"parent": doctype})
		
		# Add new permissions
		doc = frappe.get_doc("DocType", doctype)
		doc.permissions = []
		
		for perm in perms:
			doc.append("permissions", perm)
		
		doc.save()
		frappe.db.commit()


@frappe.whitelist()
def has_cv_access(cv_profile_name, user=None):
	"""Check if user has access to CV Profile"""
	if not user:
		user = frappe.session.user
	
	if user == "Administrator":
		return True
	
	cv_profile = frappe.get_doc("Employee CV Profile", cv_profile_name)
	
	# Check if user is the employee owner
	employee_user = frappe.db.get_value("Employee", cv_profile.employee, "user_id")
	if employee_user == user:
		return True
	
	# Check if user has HR Manager role
	if "HR Manager" in frappe.get_roles(user):
		return True
	
	# Check if user has System Manager role
	if "System Manager" in frappe.get_roles(user):
		return True
	
	return False


@frappe.whitelist()
def get_accessible_cv_profiles(user=None):
	"""Get list of CV profiles accessible to user"""
	if not user:
		user = frappe.session.user
	
	if user == "Administrator" or "System Manager" in frappe.get_roles(user):
		# System Manager can see all profiles
		return frappe.get_all("Employee CV Profile", fields=["name", "employee", "employee_name", "cv_title"])
	
	elif "HR Manager" in frappe.get_roles(user):
		# HR Manager can see all profiles
		return frappe.get_all("Employee CV Profile", fields=["name", "employee", "employee_name", "cv_title"])
	
	elif "Employee" in frappe.get_roles(user):
		# Employee can only see their own profile
		employee = frappe.db.get_value("Employee", {"user_id": user}, "name")
		if employee:
			return frappe.get_all(
				"Employee CV Profile", 
				filters={"employee": employee},
				fields=["name", "employee", "employee_name", "cv_title"]
			)
	
	return []


def setup_user_permissions():
	"""Setup user permissions for employees to access only their CV"""
	employees = frappe.get_all("Employee", filters={"user_id": ["!=", ""]}, fields=["name", "user_id"])
	
	for emp in employees:
		if emp.user_id:
			# Check if CV Profile exists
			cv_profile = frappe.db.get_value("Employee CV Profile", {"employee": emp.name}, "name")
			if cv_profile:
				# Create user permission
				if not frappe.db.exists("User Permission", {
					"user": emp.user_id,
					"allow": "Employee CV Profile",
					"for_value": cv_profile
				}):
					user_perm = frappe.new_doc("User Permission")
					user_perm.user = emp.user_id
					user_perm.allow = "Employee CV Profile"
					user_perm.for_value = cv_profile
					user_perm.save()


@frappe.whitelist()
def validate_public_token_access(token):
	"""Validate public token and return access info"""
	if not token:
		return {"valid": False, "message": "Token is required"}
	
	cv_profile = frappe.db.get_value("Employee CV Profile", {"public_token": token}, "name")
	if not cv_profile:
		return {"valid": False, "message": "Invalid token"}
	
	doc = frappe.get_doc("Employee CV Profile", cv_profile)
	if not doc.is_token_valid():
		return {"valid": False, "message": "Token has expired"}
	
	# Check visibility settings
	if doc.cv_visibility == "Private":
		return {"valid": False, "message": "This CV is private"}
	
	return {
		"valid": True,
		"cv_profile": cv_profile,
		"visibility": doc.cv_visibility,
		"valid_until": doc.token_valid_until
	}
