2025-07-14 09:38:33,389 INFO ipython === bench console session ===
2025-07-14 09:38:33,389 INFO ipython import frappe
2025-07-14 09:38:33,389 INFO ipython frappe.get_all("DocType", filters={"module": "CV Manager"})
2025-07-14 09:38:33,390 INFO ipython from cv_maker.fixtures.test_data import create_test_data
2025-07-14 09:38:33,390 INFO ipython result = create_test_data()
2025-07-14 09:38:33,390 INFO ipython frappe.get_all("Company", limit=1)
2025-07-14 09:38:33,390 INFO ipython frappe.get_all("Employee", limit=1)
2025-07-14 09:38:33,390 INFO ipython template = frappe.new_doc("CV Template")
2025-07-14 09:38:33,391 INFO ipython template.template_name = "Test Template"
2025-07-14 09:38:33,391 INFO ipython template.template_html = "<h1>{{ employee_name }}</h1>"
2025-07-14 09:38:33,391 INFO ipython template.status = "Active"
2025-07-14 09:38:33,391 INFO ipython # First, let's create a Company
2025-07-14 09:38:33,391 INFO ipython company = frappe.new_doc("Company")
2025-07-14 09:38:33,392 INFO ipython company.company_name = "Mysite Co."
2025-07-14 09:38:33,392 INFO ipython company.abbr = "MSC"
2025-07-14 09:38:33,392 INFO ipython company.default_currency = "USD"
2025-07-14 09:38:33,392 INFO ipython company.insert(ignore_permissions=True)
2025-07-14 09:38:33,393 INFO ipython company.country = "United States"
2025-07-14 09:38:33,393 INFO ipython company.insert(ignore_permissions=True)
2025-07-14 09:38:33,393 INFO ipython frappe.get_all("Company")
2025-07-14 09:38:33,393 INFO ipython frappe.db.sql("SELECT name FROM `tabCompany`")
2025-07-14 09:38:33,393 INFO ipython frappe.db.commit()
2025-07-14 09:38:33,394 INFO ipython company = frappe.get_doc("Company", "Mysite Co.")
2025-07-14 09:38:33,394 INFO ipython # Create departments
2025-07-14 09:38:33,394 INFO ipython departments = ["Technology", "Product", "Design", "Analytics", "Human Resources"]
2025-07-14 09:38:33,394 INFO ipython for dept_name in departments:
        if not frappe.db.exists("Department", dept_name):
                    dept = frappe.new_doc("Department")
                            dept.department_name = dept_name
2025-07-14 09:38:33,394 INFO ipython         dept.company = "Mysite Co."
2025-07-14 09:38:33,395 INFO ipython         dept.insert(ignore_permissions=True)
2025-07-14 09:38:33,395 INFO ipython         print(f"Created department: {dept_name}")
2025-07-14 09:38:33,395 INFO ipython for dept_name in departments:
        if not frappe.db.exists("Department", dept_name):
                    dept = frappe.new_doc("Department")
                            dept.department_name = dept_name
2025-07-14 09:38:33,395 INFO ipython         dept.company = "Mysite Co."
2025-07-14 09:38:33,395 INFO ipython         dept.insert(ignore_permissions=True)
2025-07-14 09:38:33,396 INFO ipython         print(f"Created department: {dept_name}")
2025-07-14 09:38:33,396 INFO ipython === session end ===
