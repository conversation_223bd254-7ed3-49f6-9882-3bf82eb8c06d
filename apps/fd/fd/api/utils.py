# Copyright (c) 2021, Aakvatech Limited and contributors
# For license information, please see license.txt

import json
import frappe
import requests
from frappe import _
from time import sleep
from frappe.utils import now
from frappe.installer import update_site_config, get_site_config_path



def update_doc(doc, doctype=None, force_name=None, action=None, permission=True):
    cur_doc = None
    if not doc.get("doctype") and not doctype:
        frappe.throw(_("doctype is required"))
    if not doc.get("doctype"):
        doc["doctype"] = doctype
    if permission:
        frappe.has_permission(doc.get("doctype"), "write", throw=True)
    if doc.get("name"):
        if frappe.db.exists(doc.get("doctype"), doc.get("name")):
            cur_doc = frappe.get_doc(doc.get("doctype"), doc.get("name"))
            doc["modified"] = cur_doc.modified
            cur_doc.update(doc)
            if cur_doc.docstatus == 1:
                cur_doc.db_update()
            else:
                cur_doc.save(ignore_permissions=True)
        else:
            cur_doc = frappe.new_doc(doc.get("doctype"))
            cur_doc.update(doc)
            if force_name:
                cur_doc.insert(set_name=force_name, ignore_permissions=True)
            else:
                cur_doc.insert(ignore_permissions=True)
    else:
        cur_doc = frappe.get_doc(doc)
        cur_doc.update(doc)
        if force_name:
            cur_doc.insert(set_name=force_name, ignore_permissions=True)
        else:
            cur_doc.insert(ignore_permissions=True)

    if action == "Submit":
        cur_doc.submit()
    frappe.db.commit()
    return cur_doc


def get_credentials(site_type="main"):
    url = None,
    token = None
    if site_type == "main":
        url = frappe.conf.main_site
        token = frappe.conf.main_token
    elif site_type == "fd":
        url = frappe.conf.fd_site_url
        token = frappe.conf.fd_token
    elif site_type == "mis":
        url = frappe.conf.fd_mis_site_url
        token = frappe.conf.fd_mis_token
    return url, token


def send_request(method, payload=None, params=None, type="GET", site_type="main"):
    site_url, token = get_credentials(site_type)
    headers = {
        "Authorization": f"token {token}",
    }
    url = f"{site_url}{method}"
    the_params = None
    if params:
        the_params = {"limit": 0, "filters": json.dumps(params, default=str)}
    
    data = None
    for i in range(3):
        try:
            res = requests.request(
                method=type,
                url=url,
                params=the_params,
                data={"data": json.dumps(payload, default=str)} if payload else None,
                headers=headers,
                timeout=200,
            )
            if res.ok:
                data = res.json().get("data")
            else:
                data = {}
                error = json.loads(res.text)
                error_msg = f"ExcType: {error.get('exc_type')} \n\nException: {error.get('exception')}"
                if error.get("exc"):
                    error_msg += "\n\nExc:\n" + error.get("exc")[2:-2].replace(
                        "\\n", "\n"
                    ).replace("    ", "")
                cash_sale = f"Cash Sale: {payload.get('cash_sale')} " if payload and "cash_sale" in payload else ""
                logerror(error_msg, error.get("exception"), f"{cash_sale}Send Request: {method} - Status Code: {res.status_code}")
            break
        except Exception as e:
            sleep(3 * i + 1)
            if i != 2:
                continue
            else:
                raise e
            
    return data


def get_docs(doctype, filters=None, site_type="main"):
    method = f"/api/resource/{doctype}"
    data = send_request(method=method, params=filters, site_type=site_type)
    if len(data) == 0:
        return []
    list_data = []
    for item in data:
        doc_method = f"/api/resource/{doctype}/{item.get('name')}"
        doc = send_request(method=doc_method, site_type=site_type)
        if not doc:
            continue
        list_data.append(doc)
    return list_data


def get_doc_from_site(
    doctype,
    scheduled_job_path,
    site,
    field_values={},
):
    scheduled_job_type = frappe.get_doc("Scheduled Job Type", scheduled_job_path)
    last_successful_execution = (
        scheduled_job_type.last_successful_execution or "1980-01-01 00:00:00"
    )

    data = get_docs(
        doctype,
        filters={
            "modified": [
                ">=",
                str(last_successful_execution),
            ],
            "docstatus": ["!=", 2],
            "demo_done": 0,
        },
        site_type=site,
    )
    if not data:
        return
    scheduled_job_type.last_successful_execution = now()
    scheduled_job_type.save(ignore_permissions=True)
    for doc in data:
        if field_values != {}:
            for field in field_values.keys():
                doc[field] = field_values[field]
        try:
            doc["demo_done"] = 1
            update_doc(
                doc, doctype=doctype, force_name=doc.get("name"), permission=False
            )
        except Exception as e:
            logerror(frappe.get_traceback(), e, doc["name"])
            continue
    frappe.db.commit()


def msgThrow(msg, method="throw", alert=True):
    if method == "validate":
        frappe.msgprint(msg, alert=alert)
    else:
        frappe.throw(msg)


def logerror(traceback, exception, docname):
    frappe.log_error(
        title=str(docname + " " + str(exception))[0:100],
        message=traceback
    )


def get_sync_details():
    site_config_path = get_site_config_path()

    with open(site_config_path, 'r') as file:
        config_data = json.load(file)
    
    last_sync = config_data.get("fd_last_sync")
    days_for_cash_sale = frappe.conf.delete_fd_cash_sale_after

    return last_sync, days_for_cash_sale


def update_last_sync(last_sync):
    update_site_config("fd_last_sync", str(last_sync))