# Copyright (c) 2025, Your Organization and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import now, get_url
from frappe.utils.pdf import get_pdf
import json
import io
import base64


@frappe.whitelist()
def export_cv_profile(cv_profile_name, export_format="PDF", template_name=None):
	"""Export CV Profile in specified format"""
	
	# Get CV Profile document
	cv_profile = frappe.get_doc("Employee CV Profile", cv_profile_name)
	
	# Check permissions
	if not cv_profile.has_permission("read"):
		frappe.throw("You don't have permission to access this CV")
	
	# Get template
	if template_name:
		template = frappe.get_doc("CV Template", template_name)
	else:
		from cv_maker.cv_manager.doctype.cv_template.cv_template import get_default_template
		template_data = get_default_template()
		if not template_data:
			frappe.throw("No template found. Please create a CV template first.")
		template = frappe.get_doc("CV Template", template_data["name"])
	
	# Get employee data
	employee = frappe.get_doc("Employee", cv_profile.employee)
	
	# Prepare context for template rendering
	context = {
		"cv_profile": cv_profile,
		"employee": employee,
		"employee_name": employee.employee_name,
		"cv_title": cv_profile.cv_title,
		"summary": cv_profile.summary,
		"skills": cv_profile.skills,
		"projects": cv_profile.projects,
		"certifications": cv_profile.certifications,
		"languages": cv_profile.languages,
		"designation": employee.designation,
		"department": employee.department,
		"company": employee.company,
		"date_of_joining": employee.date_of_joining,
		"personal_email": employee.personal_email,
		"cell_number": employee.cell_number,
	}
	
	# Render HTML
	html_content = frappe.render_template(template.template_html, context)
	
	# Add custom CSS if available
	if template.custom_css:
		html_content = f"<style>{template.custom_css}</style>" + html_content
	
	# Update export statistics
	cv_profile.increment_export_count()
	
	if export_format.upper() == "HTML":
		return html_content
	elif export_format.upper() == "PDF":
		return get_pdf(html_content)
	elif export_format.upper() == "DOCX":
		return export_to_docx(html_content, cv_profile)
	else:
		frappe.throw(f"Unsupported export format: {export_format}")


@frappe.whitelist()
def generate_public_link(cv_profile_name, valid_days=30):
	"""Generate a public shareable link for CV"""
	cv_profile = frappe.get_doc("Employee CV Profile", cv_profile_name)
	
	# Check permissions
	if not cv_profile.has_permission("write"):
		frappe.throw("You don't have permission to generate public link for this CV")
	
	token = cv_profile.generate_public_token(valid_days)
	public_url = get_url(f"/cv/{token}")
	
	return {
		"token": token,
		"public_url": public_url,
		"valid_until": cv_profile.token_valid_until
	}


@frappe.whitelist(allow_guest=True)
def get_public_cv(token):
	"""Get CV by public token for guest access"""
	try:
		from cv_maker.cv_manager.doctype.employee_cv_profile.employee_cv_profile import get_cv_by_token
		cv_profile = get_cv_by_token(token)
		
		# Get employee data
		employee = frappe.get_doc("Employee", cv_profile.employee)
		
		# Return sanitized data for public view
		return {
			"employee_name": employee.employee_name,
			"cv_title": cv_profile.cv_title,
			"summary": cv_profile.summary,
			"designation": employee.designation,
			"department": employee.department,
			"company": employee.company,
			"skills": cv_profile.skills,
			"projects": cv_profile.projects,
			"certifications": cv_profile.certifications,
			"languages": cv_profile.languages,
			"cv_visibility": cv_profile.cv_visibility
		}
	except Exception as e:
		frappe.throw(str(e))


@frappe.whitelist()
def get_cv_completeness_score(cv_profile_name):
	"""Get CV completeness score"""
	cv_profile = frappe.get_doc("Employee CV Profile", cv_profile_name)
	return cv_profile.get_cv_completeness_score()


@frappe.whitelist()
def sync_employee_data(employee_name):
	"""Sync employee data with CV profile"""
	# Get or create CV profile
	cv_profile_name = frappe.db.get_value("Employee CV Profile", {"employee": employee_name}, "name")

	if not cv_profile_name:
		from cv_maker.cv_manager.doctype.employee_cv_profile.employee_cv_profile import create_cv_profile_for_employee
		cv_profile_name = create_cv_profile_for_employee(employee_name)

	cv_profile = frappe.get_doc("Employee CV Profile", cv_profile_name)
	employee = frappe.get_doc("Employee", employee_name)

	# Update employee name if changed
	cv_profile.employee_name = employee.employee_name

	# Update CV title if not set
	if not cv_profile.cv_title:
		designation = employee.designation or "Professional"
		cv_profile.cv_title = f"{designation} - {employee.employee_name}"

	cv_profile.save()
	return cv_profile_name


def sync_employee_data_hook(doc, method):
	"""Hook function for Employee doctype events"""
	try:
		sync_employee_data(doc.name)
	except Exception as e:
		frappe.log_error(f"Error syncing employee data for {doc.name}: {str(e)}")


def export_to_docx(html_content, cv_profile):
	"""Export CV to DOCX format using python-docx"""
	try:
		from docx import Document
		from docx.shared import Inches
		from bs4 import BeautifulSoup
		import re
		
		# Parse HTML content
		soup = BeautifulSoup(html_content, 'html.parser')
		
		# Create new document
		doc = Document()
		
		# Add title
		title = doc.add_heading(cv_profile.cv_title or cv_profile.employee_name, 0)
		
		# Add summary
		if cv_profile.summary:
			doc.add_heading('Professional Summary', level=1)
			doc.add_paragraph(cv_profile.summary)
		
		# Add skills
		if cv_profile.skills:
			doc.add_heading('Skills', level=1)
			for skill in cv_profile.skills:
				p = doc.add_paragraph()
				p.add_run(f"{skill.skill_name}: ").bold = True
				p.add_run(f"{skill.proficiency_level}")
				if skill.years_of_experience:
					p.add_run(f" ({skill.years_of_experience} years)")
		
		# Add projects
		if cv_profile.projects:
			doc.add_heading('Projects', level=1)
			for project in cv_profile.projects:
				doc.add_heading(project.project_title, level=2)
				if project.client:
					p = doc.add_paragraph()
					p.add_run("Client: ").bold = True
					p.add_run(project.client)
				if project.role:
					p = doc.add_paragraph()
					p.add_run("Role: ").bold = True
					p.add_run(project.role)
				if project.description:
					doc.add_paragraph(project.description)
		
		# Add certifications
		if cv_profile.certifications:
			doc.add_heading('Certifications', level=1)
			for cert in cv_profile.certifications:
				p = doc.add_paragraph()
				p.add_run(f"{cert.certification_name}").bold = True
				if cert.issuing_organization:
					p.add_run(f" - {cert.issuing_organization}")
				if cert.date_awarded:
					p.add_run(f" ({cert.date_awarded})")
		
		# Add languages
		if cv_profile.languages:
			doc.add_heading('Languages', level=1)
			for lang in cv_profile.languages:
				p = doc.add_paragraph()
				p.add_run(f"{lang.language}: ").bold = True
				p.add_run(lang.proficiency)
		
		# Save to bytes
		doc_io = io.BytesIO()
		doc.save(doc_io)
		doc_io.seek(0)
		
		return doc_io.getvalue()
		
	except ImportError:
		frappe.throw("python-docx library is required for DOCX export. Please install it using: pip install python-docx")
	except Exception as e:
		frappe.throw(f"Error generating DOCX: {str(e)}")


@frappe.whitelist()
def generate_all_cvs(department=None, export_format="PDF"):
	"""Bulk generate CVs for all employees"""
	filters = {"status": "Active"}
	if department:
		filters["department"] = department
	
	employees = frappe.get_all("Employee", filters=filters, fields=["name", "employee_name"])
	
	results = []
	for emp in employees:
		try:
			# Sync employee data and get/create CV profile
			cv_profile_name = sync_employee_data(emp.name)
			
			# Export CV
			file_content = export_cv_profile(cv_profile_name, export_format)
			
			results.append({
				"employee": emp.name,
				"employee_name": emp.employee_name,
				"cv_profile": cv_profile_name,
				"status": "Success",
				"file_size": len(file_content) if file_content else 0
			})
		except Exception as e:
			results.append({
				"employee": emp.name,
				"employee_name": emp.employee_name,
				"status": "Error",
				"error": str(e)
			})
	
	return results


@frappe.whitelist()
def get_cv_statistics():
	"""Get CV statistics for dashboard"""
	total_cvs = frappe.db.count("Employee CV Profile")

	# Get completeness scores
	cv_profiles = frappe.get_all("Employee CV Profile", fields=["name"])
	complete_cvs = 0
	incomplete_cvs = 0
	total_exports = 0

	for cv in cv_profiles:
		try:
			doc = frappe.get_doc("Employee CV Profile", cv.name)
			score = doc.get_cv_completeness_score()
			total_exports += doc.export_count or 0

			if score >= 80:
				complete_cvs += 1
			else:
				incomplete_cvs += 1
		except:
			incomplete_cvs += 1

	return {
		"total_cvs": total_cvs,
		"complete_cvs": complete_cvs,
		"incomplete_cvs": incomplete_cvs,
		"total_exports": total_exports
	}


@frappe.whitelist()
def get_completeness_distribution():
	"""Get CV completeness distribution for charts"""
	cv_profiles = frappe.get_all("Employee CV Profile", fields=["name"])

	complete = 0  # 80%+
	good = 0      # 60-79%
	needs_work = 0 # <60%

	for cv in cv_profiles:
		try:
			doc = frappe.get_doc("Employee CV Profile", cv.name)
			score = doc.get_cv_completeness_score()

			if score >= 80:
				complete += 1
			elif score >= 60:
				good += 1
			else:
				needs_work += 1
		except:
			needs_work += 1

	return {
		"complete": complete,
		"good": good,
		"needs_work": needs_work
	}


@frappe.whitelist()
def get_recent_activities(limit=10):
	"""Get recent CV activities"""
	activities = []

	# Get recent CV profile updates
	recent_cvs = frappe.get_all(
		"Employee CV Profile",
		fields=["name", "employee_name", "modified", "last_exported"],
		order_by="modified desc",
		limit=limit
	)

	for cv in recent_cvs:
		activities.append({
			"timestamp": frappe.utils.pretty_date(cv.modified),
			"employee_name": cv.employee_name,
			"action": "updated their CV profile"
		})

		if cv.last_exported:
			activities.append({
				"timestamp": frappe.utils.pretty_date(cv.last_exported),
				"employee_name": cv.employee_name,
				"action": "exported their CV"
			})

	# Sort by timestamp and limit
	activities = sorted(activities, key=lambda x: x["timestamp"], reverse=True)[:limit]

	return activities


@frappe.whitelist()
def send_cv_update_reminders():
	"""Send reminders to employees with incomplete CVs"""
	from frappe.utils import add_days, today

	# Get employees with incomplete CVs or no CV at all
	employees = frappe.get_all(
		"Employee",
		filters={"status": "Active", "user_id": ["!=", ""]},
		fields=["name", "employee_name", "user_id", "personal_email"]
	)

	reminder_count = 0

	for emp in employees:
		cv_profile = frappe.db.get_value("Employee CV Profile", {"employee": emp.name}, "name")

		send_reminder = False

		if not cv_profile:
			send_reminder = True
			reason = "You don't have a CV profile yet"
		else:
			doc = frappe.get_doc("Employee CV Profile", cv_profile)
			score = doc.get_cv_completeness_score()

			if score < 60:
				send_reminder = True
				reason = f"Your CV is only {score}% complete"

		if send_reminder and emp.personal_email:
			try:
				frappe.sendmail(
					recipients=[emp.personal_email],
					subject="CV Profile Update Reminder",
					message=f"""
					Dear {emp.employee_name},

					{reason}. Please take a few minutes to update your CV profile.

					You can access your CV profile through the ERPNext system.

					Best regards,
					HR Team
					""",
					header=["CV Update Reminder", "blue"]
				)
				reminder_count += 1
			except Exception as e:
				frappe.log_error(f"Failed to send reminder to {emp.employee_name}: {str(e)}")

	return f"Sent {reminder_count} reminders successfully"
