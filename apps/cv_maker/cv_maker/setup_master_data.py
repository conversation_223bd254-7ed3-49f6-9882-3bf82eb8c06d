# Copyright (c) 2025, Your Organization and contributors
# For license information, please see license.txt

import frappe


def setup_master_data():
	"""Setup master data required for CV Manager testing"""
	
	# Create Company if it doesn't exist
	if not frappe.db.exists("Company", "Mysite Co."):
		company = frappe.new_doc("Company")
		company.company_name = "Mysite Co."
		company.abbr = "MSC"
		company.default_currency = "USD"
		company.country = "United States"
		company.insert(ignore_permissions=True)
		print("Created Company: Mysite Co.")
	else:
		print("Company 'Mysite Co.' already exists")
	
	# Create Departments
	departments = ["Technology", "Product", "Design", "Analytics", "Human Resources"]
	for dept_name in departments:
		if not frappe.db.exists("Department", dept_name):
			dept = frappe.new_doc("Department")
			dept.department_name = dept_name
			dept.company = "Mysite Co."
			dept.insert(ignore_permissions=True)
			print(f"Created Department: {dept_name}")
		else:
			print(f"Department '{dept_name}' already exists")
	
	# Create Designations
	designations = [
		"Senior Software Engineer",
		"Product Manager", 
		"UX Designer",
		"Data Analyst",
		"HR Manager",
		"Software Developer",
		"Junior Developer",
		"Senior Product Manager",
		"Lead Designer",
		"Senior Data Analyst"
	]
	
	for designation_name in designations:
		if not frappe.db.exists("Designation", designation_name):
			designation = frappe.new_doc("Designation")
			designation.designation_name = designation_name
			designation.insert(ignore_permissions=True)
			print(f"Created Designation: {designation_name}")
		else:
			print(f"Designation '{designation_name}' already exists")
	
	# Create Gender records if they don't exist
	genders = ["Male", "Female", "Other"]
	for gender in genders:
		if not frappe.db.exists("Gender", gender):
			gender_doc = frappe.new_doc("Gender")
			gender_doc.gender = gender
			gender_doc.insert(ignore_permissions=True)
			print(f"Created Gender: {gender}")
		else:
			print(f"Gender '{gender}' already exists")
	
	# Create CV Templates
	create_cv_templates()
	
	frappe.db.commit()
	print("\nMaster data setup completed successfully!")
	return True


def create_cv_templates():
	"""Create default CV templates"""
	
	templates = [
		{
			"name": "Minimalist CV",
			"template_type": "Minimalist",
			"description": "Clean and simple CV template with minimal design elements",
			"is_default": 1,
			"template_html": """
<!DOCTYPE html>
<html>
<head>
    <title>{{ employee_name }} - CV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 25px; }
        .section-title { font-size: 1.2em; font-weight: bold; margin-bottom: 10px; text-transform: uppercase; }
        .skill-item, .project-item { margin-bottom: 15px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ employee_name }}</h1>
        <h2>{{ cv_title or designation }}</h2>
    </div>
    
    {% if summary %}
    <div class="section">
        <div class="section-title">Summary</div>
        <p>{{ summary }}</p>
    </div>
    {% endif %}
    
    {% if skills %}
    <div class="section">
        <div class="section-title">Skills</div>
        {% for skill in skills %}
        <div class="skill-item">
            <strong>{{ skill.skill_name }}</strong> - {{ skill.proficiency_level }}
            {% if skill.years_of_experience %} ({{ skill.years_of_experience }} years){% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if projects %}
    <div class="section">
        <div class="section-title">Projects</div>
        {% for project in projects %}
        <div class="project-item">
            <h3>{{ project.project_title }}</h3>
            {% if project.client %}<p><strong>Client:</strong> {{ project.client }}</p>{% endif %}
            {% if project.description %}<p>{{ project.description }}</p>{% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
</body>
</html>
			"""
		},
		{
			"name": "Corporate CV",
			"template_type": "Corporate",
			"description": "Professional corporate-style CV template",
			"is_default": 0,
			"template_html": """
<!DOCTYPE html>
<html>
<head>
    <title>{{ employee_name }} - CV</title>
    <style>
        body { font-family: 'Times New Roman', serif; margin: 20px; line-height: 1.6; }
        .header { background: #1e3c72; color: white; padding: 30px; text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .section-title { font-size: 1.3em; font-weight: bold; color: #1e3c72; margin-bottom: 15px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ employee_name }}</h1>
        <h2>{{ cv_title or designation }}</h2>
    </div>
    
    {% if summary %}
    <div class="section">
        <div class="section-title">Executive Summary</div>
        <p>{{ summary }}</p>
    </div>
    {% endif %}
    
    {% if skills %}
    <div class="section">
        <div class="section-title">Core Competencies</div>
        {% for skill in skills %}
        <div>{{ skill.skill_name }} - {{ skill.proficiency_level }}</div>
        {% endfor %}
    </div>
    {% endif %}
</body>
</html>
			"""
		}
	]
	
	for template_data in templates:
		if not frappe.db.exists("CV Template", template_data["name"]):
			template = frappe.new_doc("CV Template")
			template.template_name = template_data["name"]
			template.template_type = template_data["template_type"]
			template.description = template_data["description"]
			template.is_default = template_data["is_default"]
			template.status = "Active"
			template.template_html = template_data["template_html"]
			template.insert(ignore_permissions=True)
			print(f"Created CV Template: {template_data['name']}")
		else:
			print(f"CV Template '{template_data['name']}' already exists")


if __name__ == "__main__":
	setup_master_data()
