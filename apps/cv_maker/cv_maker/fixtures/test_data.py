# Copyright (c) 2025, Your Organization and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import today, add_days


def create_test_data():
	"""Create comprehensive test data for CV Manager"""
	
	# Create test employees
	employees_data = [
		{
			"name": "EMP-TEST-001",
			"employee_name": "<PERSON>",
			"first_name": "<PERSON>",
			"last_name": "<PERSON>",
			"gender": "Male",
			"date_of_birth": "1985-03-15",
			"date_of_joining": "2018-01-15",
			"designation": "Senior Software Engineer",
			"department": "Technology",
			"company": "Mysite",
			"personal_email": "<EMAIL>",
			"cell_number": "******-0101"
		},
		{
			"name": "EMP-TEST-002",
			"employee_name": "<PERSON>",
			"first_name": "<PERSON>",
			"last_name": "<PERSON>",
			"gender": "Female",
			"date_of_birth": "1990-07-22",
			"date_of_joining": "2020-03-01",
			"designation": "Product Manager",
			"department": "Product",
			"company": "Mysite",
			"personal_email": "<EMAIL>",
			"cell_number": "******-0102"
		},
		{
			"name": "EMP-TEST-003",
			"employee_name": "<PERSON>",
			"first_name": "<PERSON>",
			"last_name": "<PERSON>",
			"gender": "Male",
			"date_of_birth": "1988-11-08",
			"date_of_joining": "2019-06-10",
			"designation": "UX Designer",
			"department": "Design",
			"company": "Mysite",
			"personal_email": "<EMAIL>",
			"cell_number": "******-0103"
		},
		{
			"name": "EMP-TEST-004",
			"employee_name": "Emily Rodriguez",
			"first_name": "Emily",
			"last_name": "Rodriguez",
			"gender": "Female",
			"date_of_birth": "1992-04-12",
			"date_of_joining": "2021-09-01",
			"designation": "Data Analyst",
			"department": "Analytics",
			"company": "Mysite",
			"personal_email": "<EMAIL>",
			"cell_number": "******-0104"
		}
	]
	
	created_employees = []
	for emp_data in employees_data:
		if not frappe.db.exists("Employee", emp_data["name"]):
			employee = frappe.new_doc("Employee")
			employee.update(emp_data)
			employee.insert(ignore_permissions=True)
			created_employees.append(employee)
		else:
			created_employees.append(frappe.get_doc("Employee", emp_data["name"]))
	
	# Create CV profiles with varying completeness levels
	cv_profiles_data = [
		{
			"employee": "EMP-TEST-001",
			"cv_title": "Senior Software Engineer - Full Stack Development",
			"summary": "Experienced software engineer with 7+ years in full-stack development, specializing in Python, JavaScript, and cloud technologies. Proven track record of leading development teams and delivering scalable solutions.",
			"cv_visibility": "Customer",
			"skills": [
				{"skill_name": "Python", "proficiency_level": "Expert", "years_of_experience": 7},
				{"skill_name": "JavaScript", "proficiency_level": "Expert", "years_of_experience": 6},
				{"skill_name": "React", "proficiency_level": "Advanced", "years_of_experience": 4},
				{"skill_name": "Node.js", "proficiency_level": "Advanced", "years_of_experience": 5},
				{"skill_name": "AWS", "proficiency_level": "Advanced", "years_of_experience": 3},
				{"skill_name": "Docker", "proficiency_level": "Intermediate", "years_of_experience": 2}
			],
			"projects": [
				{
					"project_title": "E-commerce Platform Redesign",
					"client": "RetailCorp Inc",
					"role": "Lead Developer",
					"duration_from": "2023-01-01",
					"duration_to": "2023-12-31",
					"description": "Led the complete redesign of a high-traffic e-commerce platform, improving performance by 40% and user engagement by 25%.",
					"technologies": "Python, Django, React, PostgreSQL, Redis, AWS"
				},
				{
					"project_title": "Microservices Migration",
					"client": "TechStart Solutions",
					"role": "Senior Developer",
					"duration_from": "2022-03-01",
					"duration_to": "2022-11-30",
					"description": "Migrated monolithic application to microservices architecture, reducing deployment time by 60%.",
					"technologies": "Python, FastAPI, Docker, Kubernetes, MongoDB"
				}
			],
			"certifications": [
				{
					"certification_name": "AWS Certified Solutions Architect",
					"issuing_organization": "Amazon Web Services",
					"date_awarded": "2023-05-15",
					"expiry_date": "2026-05-15"
				},
				{
					"certification_name": "Certified Kubernetes Administrator",
					"issuing_organization": "Cloud Native Computing Foundation",
					"date_awarded": "2022-09-20",
					"expiry_date": "2025-09-20"
				}
			],
			"languages": [
				{"language": "English", "proficiency": "Native"},
				{"language": "Spanish", "proficiency": "Professional"},
				{"language": "Mandarin", "proficiency": "Basic"}
			]
		},
		{
			"employee": "EMP-TEST-002",
			"cv_title": "Product Manager - Digital Innovation",
			"summary": "Strategic product manager with 5+ years experience driving digital transformation initiatives. Expert in agile methodologies, user research, and cross-functional team leadership.",
			"cv_visibility": "Internal",
			"skills": [
				{"skill_name": "Product Strategy", "proficiency_level": "Expert", "years_of_experience": 5},
				{"skill_name": "Agile/Scrum", "proficiency_level": "Expert", "years_of_experience": 5},
				{"skill_name": "User Research", "proficiency_level": "Advanced", "years_of_experience": 4},
				{"skill_name": "Data Analysis", "proficiency_level": "Advanced", "years_of_experience": 3},
				{"skill_name": "Wireframing", "proficiency_level": "Intermediate", "years_of_experience": 2}
			],
			"projects": [
				{
					"project_title": "Mobile App Launch",
					"client": "Internal Product",
					"role": "Product Manager",
					"duration_from": "2023-06-01",
					"duration_to": "2024-01-31",
					"description": "Led the development and launch of a mobile application that achieved 100K+ downloads in the first quarter.",
					"technologies": "React Native, Firebase, Analytics"
				}
			],
			"certifications": [
				{
					"certification_name": "Certified Scrum Product Owner",
					"issuing_organization": "Scrum Alliance",
					"date_awarded": "2022-03-10",
					"expiry_date": "2024-03-10"
				}
			],
			"languages": [
				{"language": "English", "proficiency": "Native"},
				{"language": "French", "proficiency": "Professional"}
			]
		},
		{
			"employee": "EMP-TEST-003",
			"cv_title": "UX Designer - Digital Experiences",
			"summary": "Creative UX designer passionate about creating intuitive and accessible digital experiences. Specialized in user-centered design and design systems.",
			"cv_visibility": "Customer",
			"skills": [
				{"skill_name": "UI/UX Design", "proficiency_level": "Expert", "years_of_experience": 6},
				{"skill_name": "Figma", "proficiency_level": "Expert", "years_of_experience": 4},
				{"skill_name": "Adobe Creative Suite", "proficiency_level": "Advanced", "years_of_experience": 8},
				{"skill_name": "Prototyping", "proficiency_level": "Advanced", "years_of_experience": 5}
			],
			"projects": [
				{
					"project_title": "Design System Implementation",
					"client": "Internal",
					"role": "Lead UX Designer",
					"duration_from": "2023-01-01",
					"duration_to": "2023-08-31",
					"description": "Created and implemented a comprehensive design system that improved design consistency across all products.",
					"technologies": "Figma, Storybook, React"
				}
			],
			"languages": [
				{"language": "English", "proficiency": "Fluent"},
				{"language": "Korean", "proficiency": "Native"}
			]
		},
		{
			"employee": "EMP-TEST-004",
			"cv_title": "Data Analyst",
			"summary": "Detail-oriented data analyst with expertise in statistical analysis and data visualization.",
			"cv_visibility": "Internal",
			"skills": [
				{"skill_name": "Python", "proficiency_level": "Advanced", "years_of_experience": 3},
				{"skill_name": "SQL", "proficiency_level": "Advanced", "years_of_experience": 3},
				{"skill_name": "Tableau", "proficiency_level": "Intermediate", "years_of_experience": 2}
			],
			# Minimal data to test incomplete CV
			"projects": [],
			"certifications": [],
			"languages": [
				{"language": "English", "proficiency": "Fluent"},
				{"language": "Spanish", "proficiency": "Native"}
			]
		}
	]
	
	created_cv_profiles = []
	for cv_data in cv_profiles_data:
		employee_name = cv_data.pop("employee")
		skills_data = cv_data.pop("skills", [])
		projects_data = cv_data.pop("projects", [])
		certifications_data = cv_data.pop("certifications", [])
		languages_data = cv_data.pop("languages", [])
		
		# Check if CV profile already exists
		existing_cv = frappe.db.get_value("Employee CV Profile", {"employee": employee_name}, "name")
		if existing_cv:
			cv_profile = frappe.get_doc("Employee CV Profile", existing_cv)
		else:
			cv_profile = frappe.new_doc("Employee CV Profile")
			cv_profile.employee = employee_name
		
		# Update basic fields
		cv_profile.update(cv_data)
		
		# Clear existing child tables
		cv_profile.skills = []
		cv_profile.projects = []
		cv_profile.certifications = []
		cv_profile.languages = []
		
		# Add skills
		for skill in skills_data:
			cv_profile.append("skills", skill)
		
		# Add projects
		for project in projects_data:
			cv_profile.append("projects", project)
		
		# Add certifications
		for cert in certifications_data:
			cv_profile.append("certifications", cert)
		
		# Add languages
		for lang in languages_data:
			cv_profile.append("languages", lang)
		
		cv_profile.save(ignore_permissions=True)
		created_cv_profiles.append(cv_profile)
	
	# Create a test CV pack
	if not frappe.db.exists("CV Pack", "Test Development Team"):
		cv_pack = frappe.new_doc("CV Pack")
		cv_pack.pack_name = "Test Development Team"
		cv_pack.description = "CV pack for the development team members"
		cv_pack.status = "Active"
		cv_pack.export_format = "PDF"
		
		# Add employees to pack
		for emp in ["EMP-TEST-001", "EMP-TEST-003"]:
			cv_pack.append("employees", {
				"employee": emp,
				"include_in_export": 1
			})
		
		cv_pack.insert(ignore_permissions=True)
	
	frappe.db.commit()
	
	return {
		"employees": len(created_employees),
		"cv_profiles": len(created_cv_profiles),
		"message": "Test data created successfully"
	}


def cleanup_test_data():
	"""Clean up test data"""
	test_employees = ["EMP-TEST-001", "EMP-TEST-002", "EMP-TEST-003", "EMP-TEST-004"]
	
	# Delete CV profiles
	for emp in test_employees:
		cv_profile = frappe.db.get_value("Employee CV Profile", {"employee": emp}, "name")
		if cv_profile:
			frappe.delete_doc("Employee CV Profile", cv_profile, ignore_permissions=True)
	
	# Delete employees
	for emp in test_employees:
		if frappe.db.exists("Employee", emp):
			frappe.delete_doc("Employee", emp, ignore_permissions=True)
	
	# Delete test CV pack
	if frappe.db.exists("CV Pack", "Test Development Team"):
		frappe.delete_doc("CV Pack", "Test Development Team", ignore_permissions=True)
	
	frappe.db.commit()
	
	return {"message": "Test data cleaned up successfully"}
