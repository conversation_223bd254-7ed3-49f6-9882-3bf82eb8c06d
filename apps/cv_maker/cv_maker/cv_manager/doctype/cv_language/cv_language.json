{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2025-01-11 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["language", "proficiency", "column_break_3", "reading", "writing", "speaking"], "fields": [{"fieldname": "language", "fieldtype": "Data", "in_list_view": 1, "label": "Language", "reqd": 1}, {"fieldname": "proficiency", "fieldtype": "Select", "in_list_view": 1, "label": "Overall Proficiency", "options": "Basic\nProfessional\nFluent\nNative", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "reading", "fieldtype": "Select", "label": "Reading", "options": "Basic\nProfessional\nFluent\nNative"}, {"fieldname": "writing", "fieldtype": "Select", "label": "Writing", "options": "Basic\nProfessional\nFluent\nNative"}, {"fieldname": "speaking", "fieldtype": "Select", "label": "Speaking", "options": "Basic\nProfessional\nFluent\nNative"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-01-11 10:00:00.000000", "modified_by": "Administrator", "module": "CV Manager", "name": "CV Language", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}