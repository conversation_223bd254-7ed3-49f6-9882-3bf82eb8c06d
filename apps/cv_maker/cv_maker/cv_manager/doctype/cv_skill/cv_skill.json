{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2025-01-11 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["skill_name", "proficiency_level", "years_of_experience", "description"], "fields": [{"fieldname": "skill_name", "fieldtype": "Data", "in_list_view": 1, "label": "Skill Name", "reqd": 1, "unique": 1}, {"fieldname": "proficiency_level", "fieldtype": "Select", "in_list_view": 1, "label": "Proficiency Level", "options": "Beginner\nIntermediate\nAdvanced\nExpert", "reqd": 1}, {"fieldname": "years_of_experience", "fieldtype": "Float", "in_list_view": 1, "label": "Years of Experience", "precision": "1"}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-01-11 10:00:00.000000", "modified_by": "Administrator", "module": "CV Manager", "name": "CV <PERSON>", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}