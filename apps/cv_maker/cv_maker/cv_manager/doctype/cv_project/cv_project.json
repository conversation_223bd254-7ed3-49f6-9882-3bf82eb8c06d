{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2025-01-11 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["project_title", "client", "role", "column_break_4", "duration_from", "duration_to", "section_break_7", "description", "technologies"], "fields": [{"fieldname": "project_title", "fieldtype": "Data", "in_list_view": 1, "label": "Project Title", "reqd": 1}, {"fieldname": "client", "fieldtype": "Data", "in_list_view": 1, "label": "Client"}, {"fieldname": "role", "fieldtype": "Data", "in_list_view": 1, "label": "Role"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "duration_from", "fieldtype": "Date", "in_list_view": 1, "label": "Duration From"}, {"fieldname": "duration_to", "fieldtype": "Date", "label": "Duration To"}, {"fieldname": "section_break_7", "fieldtype": "Section Break"}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"fieldname": "technologies", "fieldtype": "Data", "label": "Technologies Used"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-01-11 10:00:00.000000", "modified_by": "Administrator", "module": "CV Manager", "name": "CV Project", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}