2025-07-09 19:08:11,036 WARNING database DDL Query made to DB:
create table `tabAppointment Letter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_applicant` varchar(140),
`applicant_name` varchar(140),
`company` varchar(140),
`appointment_date` date,
`appointment_letter_template` varchar(140),
`introduction` longtext,
`closing_notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:11,218 WARNING database DDL Query made to DB:
create table `tabExit Interview` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`email` varchar(140),
`company` varchar(140),
`status` varchar(140),
`date` date,
`department` varchar(140),
`designation` varchar(140),
`reports_to` varchar(140),
`date_of_joining` date,
`relieving_date` date,
`ref_doctype` varchar(140),
`questionnaire_email_sent` int(1) not null default 0,
`reference_document_name` varchar(140),
`interview_summary` longtext,
`employee_status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:11,336 WARNING database DDL Query made to DB:
create table `tabInterviewer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:11,493 WARNING database DDL Query made to DB:
create table `tabEmployee Separation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`company` varchar(140),
`boarding_status` varchar(140) default 'Pending',
`resignation_letter_date` date,
`boarding_begins_on` date,
`project` varchar(140),
`employee_separation_template` varchar(140),
`notify_users_by_email` int(1) not null default 0,
`exit_interview` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:11,616 WARNING database DDL Query made to DB:
create table `tabIdentification Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`identification_document_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:11,756 WARNING database DDL Query made to DB:
create table `tabStaffing Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`designation` varchar(140),
`vacancies` int(11) not null default 0,
`estimated_cost_per_position` decimal(21,9) not null default 0,
`total_estimated_cost` decimal(21,9) not null default 0,
`current_count` int(11) not null default 0,
`current_openings` int(11) not null default 0,
`number_of_positions` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:11,926 WARNING database DDL Query made to DB:
create table `tabTravel Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`travel_type` varchar(140),
`travel_funding` varchar(140),
`travel_proof` text,
`purpose_of_travel` varchar(140),
`details_of_sponsor` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`cell_number` varchar(140),
`prefered_email` varchar(140),
`company` varchar(140),
`date_of_birth` date,
`personal_id_type` varchar(140),
`personal_id_number` varchar(140),
`passport_number` varchar(140),
`description` text,
`cost_center` varchar(140),
`name_of_organizer` varchar(140),
`address_of_organizer` varchar(140),
`other_details` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:12,052 WARNING database DDL Query made to DB:
create table `tabDesignation Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:12,181 WARNING database DDL Query made to DB:
create table `tabFull and Final Outstanding Statement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`component` varchar(140),
`reference_document_type` varchar(140),
`reference_document` varchar(140),
`account` varchar(140),
`paid_via_salary_slip` int(1) not null default 0,
`amount` decimal(21,9) not null default 0,
`status` varchar(140) default 'Unsettled',
`remark` text,
index `reference_document`(`reference_document`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:12,302 WARNING database DDL Query made to DB:
create table `tabKRA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:12,625 WARNING database DDL Query made to DB:
create table `tabGoal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal_name` varchar(140),
`is_group` int(1) not null default 0,
`parent_goal` varchar(140),
`progress` decimal(21,9) not null default 0,
`status` varchar(140) default 'Pending',
`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`user` varchar(140),
`start_date` date,
`end_date` date,
`appraisal_cycle` varchar(140),
`kra` varchar(140),
`description` longtext,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:12,781 WARNING database DDL Query made to DB:
create table `tabJob Offer Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`offer_term` varchar(140),
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:12,916 WARNING database DDL Query made to DB:
create table `tabFull and Final Asset` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference` varchar(140),
`asset_name` varchar(140),
`date` datetime(6),
`actual_cost` decimal(21,9) not null default 0,
`cost` decimal(21,9) not null default 0,
`account` varchar(140),
`action` varchar(140) default 'Return',
`status` varchar(140),
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:13,076 WARNING database DDL Query made to DB:
create table `tabJob Requisition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`designation` varchar(140),
`department` varchar(140),
`no_of_positions` int(11) not null default 0,
`expected_compensation` decimal(21,9) not null default 0,
`company` varchar(140),
`status` varchar(140),
`requested_by` varchar(140),
`requested_by_name` varchar(140),
`requested_by_dept` varchar(140),
`requested_by_designation` varchar(140),
`posting_date` date,
`completed_on` date,
`expected_by` date,
`time_to_fill` decimal(21,9),
`description` longtext,
`reason_for_requesting` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:13,209 WARNING database DDL Query made to DB:
create table `tabEmployee Property History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`property` varchar(140),
`current` varchar(140),
`new` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:13,395 WARNING database DDL Query made to DB:
create table `tabLeave Encashment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`leave_period` varchar(140),
`leave_type` varchar(140),
`leave_allocation` varchar(140),
`leave_balance` decimal(21,9) not null default 0,
`actual_encashable_days` decimal(21,9) not null default 0,
`encashment_days` decimal(21,9) not null default 0,
`encashment_amount` decimal(21,9) not null default 0,
`pay_via_payment_entry` int(1) not null default 0,
`expense_account` varchar(140),
`payable_account` varchar(140),
`posting_date` date,
`currency` varchar(140),
`paid_amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`encashment_date` date,
`additional_salary` varchar(140),
`amended_from` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:13,564 WARNING database DDL Query made to DB:
create table `tabJob Offer Term Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:13,703 WARNING database DDL Query made to DB:
create table `tabEmployee Training` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training` varchar(140),
`training_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:13,834 WARNING database DDL Query made to DB:
create table `tabVehicle Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_item` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:13,966 WARNING database DDL Query made to DB:
create table `tabVehicle Service` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_item` varchar(140),
`type` varchar(140),
`frequency` varchar(140),
`expense_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:14,124 WARNING database DDL Query made to DB:
create table `tabShift Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_time` time(6),
`end_time` time(6),
`holiday_list` varchar(140),
`color` varchar(140) default 'Blue',
`enable_auto_attendance` int(1) not null default 0,
`determine_check_in_and_check_out` varchar(140),
`working_hours_calculation_based_on` varchar(140),
`begin_check_in_before_shift_start_time` int(11) not null default 60,
`allow_check_out_after_shift_end_time` int(11) not null default 60,
`mark_auto_attendance_on_holidays` int(1) not null default 0,
`working_hours_threshold_for_half_day` decimal(21,9) not null default 0,
`working_hours_threshold_for_absent` decimal(21,9) not null default 0,
`process_attendance_after` date,
`last_sync_of_checkin` datetime(6),
`auto_update_last_sync` int(1) not null default 0,
`enable_late_entry_marking` int(1) not null default 0,
`late_entry_grace_period` int(11) not null default 0,
`enable_early_exit_marking` int(1) not null default 0,
`early_exit_grace_period` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:14,281 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:14,404 WARNING database DDL Query made to DB:
create sequence if not exists pwa_notification_id_seq nocache nocycle
2025-07-09 19:08:14,436 WARNING database DDL Query made to DB:
create table `tabPWA Notification` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`to_user` varchar(140),
`from_user` varchar(140),
`message` longtext,
`read` int(1) not null default 0,
`reference_document_type` varchar(140),
`reference_document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `to_user`(`to_user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:14,582 WARNING database DDL Query made to DB:
create table `tabLeave Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`is_active` int(1) not null default 0,
`company` varchar(140),
`optional_holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:14,736 WARNING database DDL Query made to DB:
create table `tabExpense Claim Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee_advance` varchar(140),
`posting_date` date,
`advance_paid` decimal(21,9) not null default 0,
`unclaimed_amount` decimal(21,9) not null default 0,
`return_amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`advance_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:14,852 WARNING database DDL Query made to DB:
create table `tabEmployment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee_type_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:15,012 WARNING database DDL Query made to DB:
create table `tabCompensatory Leave Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`leave_type` varchar(140),
`leave_allocation` varchar(140),
`work_from_date` date,
`work_end_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`reason` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:15,176 WARNING database DDL Query made to DB:
create table `tabEmployee Health Insurance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`health_insurance_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:15,358 WARNING database DDL Query made to DB:
create table `tabInterview` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interview_round` varchar(140),
`job_applicant` varchar(140),
`job_opening` varchar(140),
`designation` varchar(140),
`resume_link` varchar(140),
`status` varchar(140) default 'Pending',
`scheduled_on` date,
`from_time` time(6),
`to_time` time(6),
`expected_average_rating` decimal(3,2),
`average_rating` decimal(3,2),
`interview_summary` text,
`reminded` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:15,535 WARNING database DDL Query made to DB:
create table `tabShift Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_type` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`status` varchar(140) default 'Draft',
`company` varchar(140),
`approver` varchar(140),
`from_date` date,
`to_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:15,705 WARNING database DDL Query made to DB:
create table `tabEmployee Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transfer_date` date,
`company` varchar(140),
`new_company` varchar(140),
`department` varchar(140),
`reallocate_leaves` int(1) not null default 0,
`create_new_employee_id` int(1) not null default 0,
`new_employee_id` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:15,860 WARNING database DDL Query made to DB:
create table `tabShift Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_type` varchar(140),
`frequency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:16,033 WARNING database DDL Query made to DB:
create table `tabAppraisal Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_title` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:16,212 WARNING database DDL Query made to DB:
create table `tabAttendance Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`from_date` date,
`to_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`include_holidays` int(1) not null default 0,
`shift` varchar(140),
`reason` varchar(140),
`explanation` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:16,390 WARNING database DDL Query made to DB:
create table `tabInterview Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interview` varchar(140),
`interview_round` varchar(140),
`job_applicant` varchar(140),
`interviewer` varchar(140),
`result` varchar(140),
`average_rating` decimal(3,2),
`feedback` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:16,579 WARNING database DDL Query made to DB:
create table `tabEmployee Performance Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`company` varchar(140),
`reviewer` varchar(140),
`reviewer_name` varchar(140),
`reviewer_designation` varchar(140),
`user` varchar(140),
`added_on` datetime(6),
`appraisal_cycle` varchar(140),
`appraisal` varchar(140),
`total_score` decimal(21,9) not null default 0,
`feedback` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:16,733 WARNING database DDL Query made to DB:
create table `tabEmployee Feedback Rating` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
`rating` decimal(3,2),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:16,866 WARNING database DDL Query made to DB:
create table `tabShift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`checkin_radius` int(11) not null default 0,
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`geolocation` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:17,014 WARNING database DDL Query made to DB:
create table `tabLeave Block List Allow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allow_user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:17,181 WARNING database DDL Query made to DB:
create table `tabEmployee Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`date` date,
`status` varchar(140),
`for_designation` varchar(140),
`email` varchar(140) unique,
`contact_no` varchar(140),
`resume_link` varchar(140),
`current_employer` varchar(140),
`current_job_title` varchar(140),
`resume` text,
`referrer` varchar(140),
`referrer_name` varchar(140),
`is_applicable_for_referral_bonus` int(1) not null default 1,
`referral_payment_status` varchar(140),
`department` varchar(140),
`qualification_reason` longtext,
`work_references` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:17,327 WARNING database DDL Query made to DB:
create table `tabEmployee Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
`proficiency` decimal(3,2),
`evaluation_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:17,457 WARNING database DDL Query made to DB:
create table `tabExpense Claim Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_date` date,
`expense_type` varchar(140),
`default_account` varchar(140),
`description` longtext,
`amount` decimal(21,9) not null default 0,
`sanctioned_amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`project` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:18,839 WARNING database DDL Query made to DB:
create table `tabGratuity Rule Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_year` int(11) not null default 0,
`to_year` int(11) not null default 0,
`fraction_of_applicable_earnings` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:18,964 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Sub Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:19,110 WARNING database DDL Query made to DB:
create table `tabSalary Withholding Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`is_salary_released` int(1) not null default 0,
`journal_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:19,285 WARNING database DDL Query made to DB:
create table `tabAdditional Salary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`is_recurring` int(1) not null default 0,
`disabled` int(1) not null default 0,
`from_date` date,
`to_date` date,
`payroll_date` date,
`amended_from` varchar(140),
`salary_component` varchar(140),
`type` varchar(140),
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`overwrite_salary_structure_amount` int(1) not null default 1,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `payroll_date`(`payroll_date`),
index `salary_component`(`salary_component`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:19,484 WARNING database DDL Query made to DB:
create table `tabSalary Structure Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`salary_structure` varchar(140),
`from_date` date,
`income_tax_slab` varchar(140),
`company` varchar(140),
`payroll_payable_account` varchar(140),
`currency` varchar(140),
`base` decimal(21,9) not null default 0,
`variable` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`taxable_earnings_till_date` decimal(21,9) not null default 0,
`tax_deducted_till_date` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `salary_structure`(`salary_structure`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:19,665 WARNING database DDL Query made to DB:
create table `tabEmployee Other Income` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`source` varchar(140),
`amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `company`(`company`),
index `payroll_period`(`payroll_period`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:19,827 WARNING database DDL Query made to DB:
create table `tabEmployee Incentive` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`department` varchar(140),
`salary_component` varchar(140),
`currency` varchar(140),
`payroll_date` date,
`incentive_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:19,998 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`total_declared_amount` decimal(21,9) not null default 0,
`total_exemption_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:20,180 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`effective_from` date,
`company` varchar(140),
`currency` varchar(140),
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`allow_tax_exemption` int(1) not null default 0,
`amended_from` varchar(140),
`tax_relief_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:20,355 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`claim_date` date,
`currency` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`earning_component` varchar(140),
`max_amount_eligible` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`claimed_amount` decimal(21,9) not null default 0,
`salary_slip` varchar(140),
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:20,533 WARNING database DDL Query made to DB:
create table `tabPayroll Employee Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`is_salary_withheld` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:20,675 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`type_of_proof` varchar(140),
`attach_proof` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:20,871 WARNING database DDL Query made to DB:
create table `tabSalary Structure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`letter_head` varchar(140),
`is_active` varchar(140) default 'Yes',
`is_default` varchar(140) default 'No',
`currency` varchar(140),
`amended_from` varchar(140),
`leave_encashment_amount_per_day` decimal(21,9) not null default 0,
`max_benefits` decimal(21,9) not null default 0,
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140) default 'Monthly',
`salary_component` varchar(140),
`hour_rate` decimal(21,9) not null default 0,
`total_earning` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`payment_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `currency`(`currency`),
index `salary_slip_based_on_timesheet`(`salary_slip_based_on_timesheet`),
index `payroll_frequency`(`payroll_frequency`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:21,025 WARNING database DDL Query made to DB:
create table `tabPayroll Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`start_date` date,
`end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:21,206 WARNING database DDL Query made to DB:
create table `tabGratuity Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disable` int(1) not null default 0,
`calculate_gratuity_amount_based_on` varchar(140),
`total_working_days_per_year` int(11) not null default 365,
`work_experience_calculation_function` varchar(140) default 'Round off Work Experience',
`minimum_year_for_gratuity` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:21,404 WARNING database DDL Query made to DB:
create table `tabSalary Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`abbr` varchar(140),
`amount` decimal(21,9) not null default 0,
`year_to_date` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
`is_recurring_additional_salary` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`depends_on_payment_days` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`is_tax_applicable` int(1) not null default 0,
`is_flexible_benefit` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`condition` longtext,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`default_amount` decimal(21,9) not null default 0,
`additional_amount` decimal(21,9) not null default 0,
`tax_on_flexible_benefit` decimal(21,9) not null default 0,
`tax_on_additional_salary` decimal(21,9) not null default 0,
index `salary_component`(`salary_component`),
index `exempted_from_income_tax`(`exempted_from_income_tax`),
index `is_tax_applicable`(`is_tax_applicable`),
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:21,535 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab Other Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`percent` decimal(21,9) not null default 0,
`min_taxable_income` decimal(21,9) not null default 0,
`max_taxable_income` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:21,671 WARNING database DDL Query made to DB:
create table `tabPayroll Period Date` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_date` date,
`end_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:21,941 WARNING database DDL Query made to DB:
create table `tabRetention Bonus` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`date_of_joining` varchar(140),
`salary_component` varchar(140),
`bonus_amount` decimal(21,9) not null default 0,
`bonus_payment_date` date,
`currency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:22,097 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`earning_component` varchar(140),
`pay_against_benefit_claim` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:22,290 WARNING database DDL Query made to DB:
create table `tabPayroll Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`company` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`payroll_payable_account` varchar(140),
`status` varchar(140),
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`deduct_tax_for_unclaimed_employee_benefits` int(1) not null default 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` int(1) not null default 0,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`number_of_employees` int(11) not null default 0,
`validate_attendance` int(1) not null default 0,
`cost_center` varchar(140),
`project` varchar(140),
`payment_account` varchar(140),
`bank_account` varchar(140),
`salary_slips_created` int(1) not null default 0,
`salary_slips_submitted` int(1) not null default 0,
`error_message` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:22,587 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:22,724 WARNING database DDL Query made to DB:
create table `tabSalary Slip Leave` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type` varchar(140),
`total_allocated_leaves` decimal(21,9) not null default 0,
`expired_leaves` decimal(21,9) not null default 0,
`used_leaves` decimal(21,9) not null default 0,
`pending_leaves` decimal(21,9) not null default 0,
`available_leaves` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:22,889 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`currency` varchar(140),
`max_benefits` decimal(21,9) not null default 0,
`remaining_benefit` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`pro_rata_dispensed_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:23,077 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`submission_date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`total_actual_amount` decimal(21,9) not null default 0,
`exemption_amount` decimal(21,9) not null default 0,
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:23,310 WARNING database DDL Query made to DB:
create table `tabSalary Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140) unique,
`salary_component_abbr` varchar(140),
`type` varchar(140),
`description` text,
`depends_on_payment_days` int(1) not null default 1,
`is_tax_applicable` int(1) not null default 1,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`is_income_tax_component` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`round_to_the_nearest_integer` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`remove_if_zero_valued` int(1) not null default 1,
`disabled` int(1) not null default 0,
`condition` longtext,
`amount` decimal(21,9) not null default 0,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`is_flexible_benefit` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`only_tax_impact` int(1) not null default 0,
`create_separate_payment_entry_against_benefit_claim` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:23,470 WARNING database DDL Query made to DB:
create table `tabSalary Slip Loan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`loan_product` varchar(140),
`loan_account` varchar(140),
`interest_income_account` varchar(140),
`principal_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`total_payment` decimal(21,9) not null default 0,
`loan_repayment_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:23,589 WARNING database DDL Query made to DB:
create table `tabEmployee Cost Center` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cost_center` varchar(140),
`percentage` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:23,728 WARNING database DDL Query made to DB:
create table `tabGratuity Applicable Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:23,858 WARNING database DDL Query made to DB:
create table `tabTaxable Salary Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_amount` decimal(21,9) not null default 0,
`to_amount` decimal(21,9) not null default 0,
`percent_deduction` decimal(21,9) not null default 0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:24,032 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int(11) not null default 0,
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:24,172 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:24,483 WARNING database DDL Query made to DB:
create table `tabSalary Slip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`branch` varchar(140),
`posting_date` date,
`letter_head` varchar(140),
`status` varchar(140),
`salary_withholding` varchar(140),
`salary_withholding_cycle` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 1.0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`salary_structure` varchar(140),
`payroll_entry` varchar(140),
`mode_of_payment` varchar(140),
`salary_slip_based_on_timesheet` int(1) not null default 0,
`deduct_tax_for_unclaimed_employee_benefits` int(1) not null default 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` int(1) not null default 0,
`total_working_days` decimal(21,9) not null default 0,
`unmarked_days` decimal(21,9) not null default 0,
`leave_without_pay` decimal(21,9) not null default 0,
`absent_days` decimal(21,9) not null default 0,
`payment_days` decimal(21,9) not null default 0,
`total_working_hours` decimal(21,9) not null default 0,
`hour_rate` decimal(21,9) not null default 0,
`base_hour_rate` decimal(21,9) not null default 0,
`gross_pay` decimal(21,9) not null default 0,
`base_gross_pay` decimal(21,9) not null default 0,
`gross_year_to_date` decimal(21,9) not null default 0,
`base_gross_year_to_date` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`base_total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`base_net_pay` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`year_to_date` decimal(21,9) not null default 0,
`base_year_to_date` decimal(21,9) not null default 0,
`month_to_date` decimal(21,9) not null default 0,
`base_month_to_date` decimal(21,9) not null default 0,
`total_in_words` varchar(240),
`base_total_in_words` varchar(240),
`ctc` decimal(21,9) not null default 0,
`income_from_other_sources` decimal(21,9) not null default 0,
`total_earnings` decimal(21,9) not null default 0,
`non_taxable_earnings` decimal(21,9) not null default 0,
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`tax_exemption_declaration` decimal(21,9) not null default 0,
`deductions_before_tax_calculation` decimal(21,9) not null default 0,
`annual_taxable_amount` decimal(21,9) not null default 0,
`income_tax_deducted_till_date` decimal(21,9) not null default 0,
`current_month_income_tax` decimal(21,9) not null default 0,
`future_income_tax_deductions` decimal(21,9) not null default 0,
`total_income_tax` decimal(21,9) not null default 0,
`journal_entry` varchar(140),
`amended_from` varchar(140),
`bank_name` varchar(140),
`bank_account_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `salary_structure`(`salary_structure`),
index `payroll_entry`(`payroll_entry`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:24,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip`
				ADD INDEX IF NOT EXISTS `employee_start_date_end_date_index`(employee, start_date, end_date)
2025-07-09 19:08:24,706 WARNING database DDL Query made to DB:
create table `tabSalary Slip Timesheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`time_sheet` varchar(140),
`working_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:24,859 WARNING database DDL Query made to DB:
create table `tabGratuity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`current_work_experience` decimal(21,9) not null default 0,
`posting_date` date,
`gratuity_rule` varchar(140),
`status` varchar(140) default 'Draft',
`company` varchar(140),
`amended_from` varchar(140),
`pay_via_salary_slip` int(1) not null default 1,
`amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`payroll_date` date,
`salary_component` varchar(140),
`cost_center` varchar(140),
`mode_of_payment` varchar(140),
`expense_account` varchar(140),
`payable_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:25,005 WARNING database DDL Query made to DB:
create table `tabSalary Component Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:08:27,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-07-09 19:08:27,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-07-09 19:08:27,566 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_expense_claim_payable_account` varchar(140), ADD COLUMN `default_employee_advance_account` varchar(140), ADD COLUMN `default_payroll_payable_account` varchar(140)
2025-07-09 19:08:27,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-07-09 19:08:27,688 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-07-09 19:08:27,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_billing_amount` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0
2025-07-09 19:08:27,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` ADD COLUMN `salary_slip` varchar(140)
2025-07-09 19:08:27,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` MODIFY `total_billable_hours` decimal(21,9) not null default 0, MODIFY `total_billed_hours` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `base_total_costing_amount` decimal(21,9) not null default 0, MODIFY `base_total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0
2025-07-09 19:08:27,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employment_type` varchar(140), ADD COLUMN `grade` varchar(140), ADD COLUMN `job_applicant` varchar(140), ADD COLUMN `default_shift` varchar(140), ADD COLUMN `expense_approver` varchar(140), ADD COLUMN `leave_approver` varchar(140), ADD COLUMN `shift_request_approver` varchar(140), ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `health_insurance_provider` varchar(140), ADD COLUMN `health_insurance_no` varchar(140)
2025-07-09 19:08:27,913 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-09 19:08:27,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerms and Conditions` ADD COLUMN `hr` int(1) not null default 1
2025-07-09 19:08:28,030 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation` ADD COLUMN `appraisal_template` varchar(140)
2025-07-09 19:08:28,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment` ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `leave_block_list` varchar(140)
2025-07-09 19:09:04,713 WARNING database DDL Query made to DB:
create table `tabPiecework Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`task_code` varchar(140) unique,
`task_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`uom` varchar(140),
`disabled` int(1) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:04,850 WARNING database DDL Query made to DB:
create table `tabSalary Slip OT Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`no_of_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:04,974 WARNING database DDL Query made to DB:
create table `tabAV Report Extension` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report` varchar(140) unique,
`active` int(1) not null default 0,
`script` longtext,
`html_format` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:05,161 WARNING database DDL Query made to DB:
create table `tabTZ Insurance Cover Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle` varchar(140),
`covernotenumber` varchar(140) unique,
`covernotestartdate` varchar(140),
`covernoteenddate` varchar(140),
`stickernumber` varchar(140),
`covernotereferencenumber` varchar(140),
`productname` varchar(140),
`classofbusiness` varchar(140),
`transactingcompany` varchar(140),
`transactingcompanytype` varchar(140),
`covernotedescription` varchar(1000),
`officername` varchar(140),
`ismotor` int(1) not null default 0,
`isfleet` int(1) not null default 0,
`statustitle` varchar(140),
`currencycode` varchar(140),
`totalpremiumamountexcludingtax` decimal(21,9) not null default 0,
`totalpremiumamountincludingtax` decimal(21,9) not null default 0,
`commisionrate` decimal(21,9) not null default 0,
`exchangerate` decimal(21,9) not null default 0,
`commisionpaid` decimal(21,9) not null default 0,
`vat` varchar(140),
`operativeclause` varchar(140),
`officertitle` varchar(140),
`fleetidentificationnumber` varchar(140),
`fleetsize` varchar(140),
`premiumlevy` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:05,293 WARNING database DDL Query made to DB:
create table `tabTZ District` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`district` varchar(140) unique,
`region` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:05,420 WARNING database DDL Query made to DB:
create table `tabRepack Template Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_target_warehouse` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_uom` varchar(140),
`qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:05,541 WARNING database DDL Query made to DB:
create table `tabElectronic Fiscal Device` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`serial_no` varchar(140),
`location` varchar(140),
`supplier` varchar(140),
`make` varchar(140),
`model` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:05,762 WARNING database DDL Query made to DB:
create table `tabAttachment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:05,902 WARNING database DDL Query made to DB:
create table `tabPrice Change Request Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`price_list` varchar(140),
`old_price` decimal(21,9) not null default 0,
`item_name` varchar(140),
`cost` decimal(21,9) not null default 0,
`new_price` decimal(21,9) not null default 0,
`valid_from` date,
`valid_to` date,
`price_list_currency` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:06,046 WARNING database DDL Query made to DB:
create table `tabCSF TZ Bank Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`bank_account` varchar(140),
`currency` varchar(140),
`company` varchar(140),
`account` varchar(140),
`bank_supplier` varchar(140),
`exchange_rate` decimal(21,9) not null default 1.0,
`total_bank_charges` decimal(21,9) not null default 0,
`ref_pi` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:06,197 WARNING database DDL Query made to DB:
create table `tabInter Company Stock Transfer Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`item_code` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`batch_no` varchar(140),
`bom_no` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`s_warehouse` varchar(140),
`t_warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:06,405 WARNING database DDL Query made to DB:
create table `tabBackground Document Posting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`posting_type` varchar(140),
`timeout` int(11) not null default 600,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:06,543 WARNING database DDL Query made to DB:
create table `tabSingle Piecework Employees` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`task` varchar(140),
`task_name` varchar(140),
`task_rate` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:06,653 WARNING database DDL Query made to DB:
create table `tabVehicle Consignment Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`bom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:06,775 WARNING database DDL Query made to DB:
create table `tabCSF TZ Bank Charges Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`value_date` date,
`control_number` varchar(140),
`description` text,
`reference_number` varchar(140),
`debit_amount` decimal(21,9) not null default 0,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:06,917 WARNING database DDL Query made to DB:
create table `tabNMB Callback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`timestamp` datetime(6),
`reference` varchar(140),
`receipt` varchar(140),
`amount` decimal(21,9) not null default 0,
`customer_name` varchar(140),
`account_number` varchar(140),
`token` varchar(140),
`fees_token` varchar(140),
`channel` varchar(140),
`payment_entry` varchar(140),
`api_key` varchar(140),
`api_secret` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:07,212 WARNING database DDL Query made to DB:
create table `tabReporting Currency Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable_reporting_currency` int(1) not null default 0,
`reporting_currency` varchar(140),
`company` varchar(140) unique,
`recalculation_start_date` date,
`recalculation_end_date` date,
`recalculation_schedule_frequency` varchar(140),
`recalculation_schedule_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:07,348 WARNING database DDL Query made to DB:
create table `tabScheduled Auto Email Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`auto_email_report` varchar(140) unique,
`enabled` int(1) not null default 0,
`schedule` varchar(140),
`day_of_month` int(11) not null default 0,
`schedule_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:07,766 WARNING database DDL Query made to DB:
create table `tabBank Charges Pattern` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`bank_account` varchar(140),
`bank_charges_pattern` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:07,960 WARNING database DDL Query made to DB:
create table `tabEFD Z Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electronic_fiscal_device` varchar(140),
`z_no` varchar(140),
`money` decimal(21,9) not null default 0,
`z_report_date_time` datetime(6),
`receipts_issued` int(11) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`total_vat` decimal(21,9) not null default 0,
`total_turnover_ex_sr` decimal(21,9) not null default 0,
`total_turnover` decimal(21,9) not null default 0,
`allowable_difference` decimal(21,9) not null default 0,
`total_excluding_vat_ticked` decimal(21,9) not null default 0,
`total_vat_ticked` decimal(21,9) not null default 0,
`total_turnover_exempted__sp_relief_ticked` decimal(21,9) not null default 0,
`total_turnover_ticked` decimal(21,9) not null default 0,
`a_turnover` decimal(21,9) not null default 0,
`b_turnover` decimal(21,9) not null default 0,
`c_turnover` decimal(21,9) not null default 0,
`d_turnover` decimal(21,9) not null default 0,
`e_turnover` decimal(21,9) not null default 0,
`a_net_sum` decimal(21,9) not null default 0,
`b_net_sum` decimal(21,9) not null default 0,
`c_net_sum` decimal(21,9) not null default 0,
`d_net_sum` decimal(21,9) not null default 0,
`a_vat` decimal(21,9) not null default 0,
`b_vat` decimal(21,9) not null default 0,
`c_vat` decimal(21,9) not null default 0,
`d_vat` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:08,107 WARNING database DDL Query made to DB:
create table `tabPiecework Single` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:08,272 WARNING database DDL Query made to DB:
create table `tabPiecework` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`company` varchar(140),
`task` varchar(140),
`quantity` decimal(21,9) not null default 0,
`task_name` varchar(140),
`task_rate` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:08,457 WARNING database DDL Query made to DB:
create table `tabTZ Insurance Policy Holder Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`createddate` varchar(140),
`updateddate` varchar(140),
`createdby` varchar(140),
`updatedby` varchar(140),
`id` varchar(140),
`policyholdertypeid` varchar(140),
`policyholderfullname` varchar(140),
`policyholderbirthdate` varchar(140),
`policyholderidentitynumber` varchar(140),
`policyholderidentitytypeid` varchar(140),
`genderid` varchar(140),
`countryid` varchar(140),
`countrycode` varchar(140),
`districtid` varchar(140),
`districtname` varchar(140),
`regionname` varchar(140),
`locationstreet` varchar(140),
`policyholderphone1` varchar(140),
`policyholderphone2` varchar(140),
`policyholderphone3` varchar(140),
`policyholderfax` varchar(140),
`postaladdress` varchar(140),
`emailaddress` varchar(140),
`companyid` varchar(140),
`statusid` varchar(140),
`systemid` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:08,597 WARNING database DDL Query made to DB:
create table `tabVehicle Fine Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference` varchar(140) unique,
`issued_date` varchar(140),
`officer` varchar(140),
`vehicle` varchar(140),
`vehicle_doc` varchar(140),
`licence` varchar(140),
`status` varchar(140),
`offence` text,
`charge` decimal(21,9) not null default 0,
`penalty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`location` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:08,780 WARNING database DDL Query made to DB:
create table `tabEmail Salary Slips` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:08,970 WARNING database DDL Query made to DB:
create table `tabParking Bill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle` varchar(140),
`billstatus` varchar(140),
`billid` varchar(140),
`approvedby` varchar(140),
`billdescription` varchar(140),
`billpayed` int(1) not null default 0,
`billreference` varchar(140) unique,
`billedamount` decimal(21,9) not null default 0,
`billcontrolnumber` varchar(140),
`billequivalentamount` decimal(21,9) not null default 0,
`expirydate` date,
`generateddate` date,
`miscellaneousamount` decimal(21,9) not null default 0,
`payeremail` varchar(140),
`remarks` varchar(140),
`payerphone` varchar(140),
`payername` varchar(140),
`reminderflag` varchar(140),
`spsystemid` varchar(140),
`billpaytype` varchar(140),
`receivedtime` varchar(140),
`billcurrency` varchar(140),
`applicationid` varchar(140),
`collectioncode` varchar(140),
`type` varchar(140),
`createdby` varchar(140),
`itemid` varchar(140),
`parkingdetailsid` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:09,112 WARNING database DDL Query made to DB:
create table `tabDelivery Exchange Item Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_sold_or_delivered` varchar(140),
`rate_sold_or_delivered` varchar(140),
`qty_sold_or_delivered` varchar(140),
`amount_sold_or_delivered` varchar(140),
`warehouse` varchar(140),
`item_exchange` varchar(140),
`amount_exchange` varchar(140),
`uom` varchar(140),
`amended_from` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:09,252 WARNING database DDL Query made to DB:
create table `tabInter Company Stock Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`from_company` varchar(140),
`default_from_warehouse` varchar(140),
`to_company` varchar(140),
`default_to_warehouse` varchar(140),
`material_receipt` varchar(140),
`inter_company_material_request` varchar(140),
`material_issue` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:09,399 WARNING database DDL Query made to DB:
create table `tabBank Clearance Pro Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_document` varchar(140),
`payment_entry` varchar(140),
`against_account` varchar(140),
`amount` varchar(140),
`flt_amount` decimal(21,9) not null default 0,
`posting_date` date,
`cheque_number` varchar(140),
`cheque_date` date,
`clearance_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:09,521 WARNING database DDL Query made to DB:
create table `tabDynamic Price List Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`warehouse` varchar(140),
`supplier` varchar(140),
`price_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:09,645 WARNING database DDL Query made to DB:
create table `tabStation Members` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:09,791 WARNING database DDL Query made to DB:
create table `tabExpense Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`date` date,
`section` varchar(140),
`purchase_invoice` varchar(140),
`supplier` varchar(140),
`expense_type` varchar(140),
`item` varchar(140),
`bill_no` varchar(140),
`amount` decimal(21,9) not null default 0,
`attach_receipt` text,
`journal_entry` varchar(140),
`section_manager` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:10,015 WARNING database DDL Query made to DB:
create table `tabInter Company Material Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`from_company` varchar(140),
`default_from_warehouse` varchar(140),
`to_company` varchar(140),
`default_to_warehouse` varchar(140),
`inter_company_stock_transfer` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:10,147 WARNING database DDL Query made to DB:
create table `tabEmail Employee Salary Slip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`send_email` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:10,315 WARNING database DDL Query made to DB:
create table `tabSection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`section_name` varchar(140) unique,
`company` varchar(140),
`section_full_name` varchar(140),
`section_manager` varchar(140),
`business_license_due_date` date,
`workplace_license_expiry` date,
`cost_center` varchar(140),
`stock_adjustment` varchar(140),
`purchase_taxes_and_charges_template` varchar(140),
`default_cash_account` varchar(140),
`default_warehouse` varchar(140),
`cash_customer` varchar(140),
`monthly_target` decimal(21,9) not null default 0,
`cash_customer_pos_profile` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:10,502 WARNING database DDL Query made to DB:
create table `tabOpen Invoice Exchange Rate Revaluation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`revaluation_date` date,
`currency` varchar(140) default 'USD',
`exchange_rate_to_company_currency` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`journal_entry` varchar(140),
`reverse_journal_entry` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:10,653 WARNING database DDL Query made to DB:
create table `tabSpecial Closing Balance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`warehouse` varchar(140),
`shift` varchar(140),
`stock_entry` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:09:10,811 WARNING database DDL Query made to DB:
create table `tabInv ERR Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invoice_type` varchar(140),
`invoice_number` varchar(140),
`invoice_currency` varchar(140),
`invoice_amount` decimal(21,9) not null default 0,
`invoice_exchange_rate` decimal(21,9) not null default 0,
`invoice_gain_or_loss` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
