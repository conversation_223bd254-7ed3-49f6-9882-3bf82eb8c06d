2025-07-09 19:09:40,995 ERROR frappe Failed to capture exception
Site: mysite
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-10 11:17:44,638 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:24:18,556 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:24:48,624 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:24:56,746 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:25:28,840 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:28:20,850 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{"from_date":"2025-06-01","to_date":"2025-07-10"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:35:16,559 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{"from_date":"2025-06-01","to_date":"2025-07-10"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-11 15:21:54,850 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'doc': '{"docstatus":0,"doctype":"Employee","name":"new-employee-wjigafztdk","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"HR-EMP-","employee_country":"Tanzania","status":"Active","create_user_permission":1,"company":"Mysite (Demo)","pension_fund":"","employee_salary_component_limit":[],"prefered_contact_email":"","unsubscribed":0,"current_accommodation_type":"","permanent_accommodation_type":"","salary_currency":"TZS","salary_mode":"","bank_country":"Tanzania","employee_ot_component":[],"marital_status":"","blood_group":"","files":[],"education":[],"external_work_history":[],"internal_work_history":[],"leave_encashed":"","employee_country_code":"tz","bank_country_code":"tz","first_name":"Phenelist","middle_name":"Simon","last_name":"Metumba","gender":"Male","date_of_birth":"2000-02-02","date_of_retirement":"2060-02-02","date_of_joining":"2025-04-01"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-13 21:47:28,452 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'doc': '{"docstatus":0,"doctype":"Employee","name":"new-employee-zoqqrifgma","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"HR-EMP-","employee_country":"Tanzania","status":"Active","create_user_permission":1,"company":"Mysite","pension_fund":"","employee_salary_component_limit":[],"prefered_contact_email":"","unsubscribed":0,"current_accommodation_type":"","permanent_accommodation_type":"","salary_currency":"TZS","salary_mode":"","bank_country":"Tanzania","employee_ot_component":[],"marital_status":"","blood_group":"","files":[],"education":[],"external_work_history":[],"internal_work_history":[],"leave_encashed":"","employee_country_code":"tz","bank_country_code":"tz","first_name":"crispine","last_name":"Alex","gender":"Male","date_of_birth":"1980-01-01","date_of_retirement":"2040-01-01","date_of_joining":"2025-02-05","designation":"Senior Software Engineer"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-13 21:49:28,103 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'doc': '{"docstatus":0,"doctype":"Employee","name":"new-employee-cznlxwhjuc","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"HR-EMP-","employee_country":"Tanzania","status":"Active","create_user_permission":1,"company":"Mysite","pension_fund":"","employee_salary_component_limit":[],"prefered_contact_email":"","unsubscribed":0,"current_accommodation_type":"","permanent_accommodation_type":"","salary_currency":"TZS","salary_mode":"","bank_country":"Tanzania","employee_ot_component":[],"marital_status":"","blood_group":"","files":[],"education":[],"external_work_history":[],"internal_work_history":[],"leave_encashed":"","employee_country_code":"tz","bank_country_code":"tz","first_name":"Phenelist","middle_name":"Simon","last_name":"Metumba","designation":"Chief Executive Officer","gender":"Male","date_of_birth":"1970-05-06","date_of_retirement":"2030-05-06","date_of_joining":"2020-02-05"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-14 09:05:17,698 ERROR frappe Failed to capture exception
Site: mysite
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
