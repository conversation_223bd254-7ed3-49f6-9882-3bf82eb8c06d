set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250710_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-10 12:00:06.700144
Config  : ./mysite/private/backups/20250710_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250710_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250710_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-10 18:00:07.339341
Config  : ./mysite/private/backups/20250710_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250710_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
