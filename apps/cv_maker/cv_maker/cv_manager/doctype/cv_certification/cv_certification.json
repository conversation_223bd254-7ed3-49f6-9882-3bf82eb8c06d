{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2025-01-11 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["certification_name", "issuing_organization", "column_break_3", "date_awarded", "expiry_date", "section_break_6", "credential_id", "credential_url"], "fields": [{"fieldname": "certification_name", "fieldtype": "Data", "in_list_view": 1, "label": "Certification Name", "reqd": 1}, {"fieldname": "issuing_organization", "fieldtype": "Data", "in_list_view": 1, "label": "Issuing Organization", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "date_awarded", "fieldtype": "Date", "in_list_view": 1, "label": "Date Awarded"}, {"fieldname": "expiry_date", "fieldtype": "Date", "label": "Expiry Date"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "credential_id", "fieldtype": "Data", "label": "Credential ID"}, {"fieldname": "credential_url", "fieldtype": "Data", "label": "Credential URL"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-01-11 10:00:00.000000", "modified_by": "Administrator", "module": "CV Manager", "name": "CV Certification", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}